package eu.torvian.chatbot.app.viewmodel.state

import eu.torvian.chatbot.common.models.ChatMessage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Manages the state of streaming messages during chat interactions.
 * 
 * This class is responsible for:
 * - Holding temporary streaming user and assistant messages
 * - Providing reactive state updates for streaming content
 * - Managing the lifecycle of streaming messages
 * 
 * During streaming:
 * 1. User message is created and stored temporarily
 * 2. Assistant message is created and updated incrementally
 * 3. Both messages are cleared when streaming completes
 */
class StreamingStateManager {
    
    private val _streamingUserMessage = MutableStateFlow<ChatMessage.UserMessage?>(null)
    
    /**
     * The temporary user message during streaming.
     * This is set when the user message is saved but before the assistant response completes.
     */
    val streamingUserMessage: StateFlow<ChatMessage.UserMessage?> = _streamingUserMessage.asStateFlow()
    
    private val _streamingAssistantMessage = MutableStateFlow<ChatMessage.AssistantMessage?>(null)
    
    /**
     * The actively streaming assistant message.
     * This message's content is updated incrementally as the stream progresses.
     */
    val streamingAssistantMessage: StateFlow<ChatMessage.AssistantMessage?> = _streamingAssistantMessage.asStateFlow()
    
    /**
     * Indicates whether streaming is currently active.
     */
    val isStreaming: Boolean
        get() = _streamingUserMessage.value != null || _streamingAssistantMessage.value != null
    
    /**
     * Sets the temporary user message during streaming.
     * 
     * @param message The user message that was saved
     */
    fun setStreamingUserMessage(message: ChatMessage.UserMessage) {
        _streamingUserMessage.value = message
    }
    
    /**
     * Sets the initial assistant message for streaming.
     * 
     * @param message The initial assistant message
     */
    fun setStreamingAssistantMessage(message: ChatMessage.AssistantMessage) {
        _streamingAssistantMessage.value = message
    }
    
    /**
     * Updates the content of the currently streaming assistant message.
     * 
     * @param deltaContent The new content to append to the current message
     */
    fun updateStreamingAssistantContent(deltaContent: String) {
        _streamingAssistantMessage.value?.let { currentMessage ->
            _streamingAssistantMessage.value = currentMessage.copy(
                content = currentMessage.content + deltaContent
            )
        }
    }
    
    /**
     * Gets the current streaming assistant message content.
     * 
     * @return The current content or empty string if no message is streaming
     */
    fun getCurrentStreamingContent(): String {
        return _streamingAssistantMessage.value?.content ?: ""
    }
    
    /**
     * Clears the streaming user message.
     */
    fun clearStreamingUserMessage() {
        _streamingUserMessage.value = null
    }
    
    /**
     * Clears the streaming assistant message.
     */
    fun clearStreamingAssistantMessage() {
        _streamingAssistantMessage.value = null
    }
    
    /**
     * Clears all streaming state.
     * This should be called when streaming completes or encounters an error.
     */
    fun clearAllStreamingState() {
        _streamingUserMessage.value = null
        _streamingAssistantMessage.value = null
    }
    
    /**
     * Gets both streaming messages as a list for display purposes.
     * 
     * @return List containing non-null streaming messages in order (user first, then assistant)
     */
    fun getStreamingMessages(): List<ChatMessage> {
        return listOfNotNull(_streamingUserMessage.value, _streamingAssistantMessage.value)
    }
}
