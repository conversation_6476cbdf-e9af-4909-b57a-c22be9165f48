package eu.torvian.chatbot.app.viewmodel.state

import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.UpdateSessionLeafMessageRequest
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

/**
 * Manages the state of the currently loaded chat session.
 * 
 * This class is responsible for:
 * - Loading and holding the current session state
 * - Managing the current branch leaf message ID
 * - Updating session properties like leaf message ID
 * - Providing reactive state updates via StateFlow
 * 
 * @property sessionApi The API client for session-related operations
 * @property scope The coroutine scope for launching operations
 * @property dispatcher The dispatcher for coroutine execution
 * @property clock The clock for timestamping updates
 */
class SessionStateManager(
    private val sessionApi: SessionApi,
    private val scope: CoroutineScope,
    private val dispatcher: CoroutineDispatcher,
    private val clock: Clock = Clock.System
) {
    
    private val _sessionState = MutableStateFlow<UiState<ApiError, ChatSession>>(UiState.Idle)
    
    /**
     * The state of the currently loaded chat session, including all messages.
     * When in Success state, provides the ChatSession object.
     */
    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = _sessionState.asStateFlow()
    
    private val _currentBranchLeafId = MutableStateFlow<Long?>(null)
    
    /**
     * The ID of the leaf message in the currently displayed thread branch.
     * Changing this triggers the UI to show a different branch.
     * Null if the session is empty or not loaded/successful.
     */
    val currentBranchLeafId: StateFlow<Long?> = _currentBranchLeafId.asStateFlow()
    
    /**
     * Gets the current session data if available and in Success state.
     * @return The ChatSession data or null if not available
     */
    val currentSession: ChatSession?
        get() = _sessionState.value.dataOrNull
    
    /**
     * Loads a session by ID and updates the state.
     * 
     * @param sessionId The ID of the session to load
     * @param onError Callback for handling errors during loading
     */
    fun loadSession(sessionId: Long, onError: (ApiError) -> Unit = {}) {
        scope.launch(dispatcher) {
            _sessionState.value = UiState.Loading
            
            sessionApi.getSessionDetails(sessionId).fold(
                ifLeft = { error ->
                    _sessionState.value = UiState.Error(error)
                    onError(error)
                },
                ifRight = { session ->
                    _sessionState.value = UiState.Success(session)
                    _currentBranchLeafId.value = session.currentLeafMessageId
                }
            )
        }
    }
    
    /**
     * Updates the current session state with new data.
     * 
     * @param updatedSession The updated session data
     */
    fun updateSession(updatedSession: ChatSession) {
        _sessionState.value = UiState.Success(updatedSession)
    }
    
    /**
     * Updates the current branch leaf message ID locally.
     * 
     * @param leafId The new leaf message ID
     */
    fun updateCurrentBranchLeafId(leafId: Long?) {
        _currentBranchLeafId.value = leafId
    }
    
    /**
     * Updates the session's leaf message ID both locally and on the backend.
     * 
     * @param leafId The new leaf message ID
     * @param onError Callback for handling errors during the update
     * @param onSuccess Callback for successful update
     */
    fun updateSessionLeafMessage(
        leafId: Long,
        onError: (ApiError) -> Unit = {},
        onSuccess: () -> Unit = {}
    ) {
        val session = currentSession ?: return
        
        scope.launch(dispatcher) {
            // Optimistically update UI state first for responsiveness
            _currentBranchLeafId.value = leafId
            
            // Persist the change to the backend
            sessionApi.updateSessionLeafMessage(
                session.id, 
                UpdateSessionLeafMessageRequest(leafId)
            ).fold(
                ifLeft = { error ->
                    onError(error)
                },
                ifRight = {
                    // Update the session object with the new leaf message ID
                    val updatedSession = session.copy(currentLeafMessageId = leafId)
                    _sessionState.value = UiState.Success(updatedSession)
                    onSuccess()
                }
            )
        }
    }
    
    /**
     * Adds new messages to the current session.
     * 
     * @param newMessages The messages to add
     * @param newLeafId The new leaf message ID (optional)
     */
    fun addMessagesToSession(newMessages: List<eu.torvian.chatbot.common.models.ChatMessage>, newLeafId: Long? = null) {
        val session = currentSession ?: return
        
        val updatedMessages = session.messages + newMessages
        val finalLeafId = newLeafId ?: newMessages.lastOrNull()?.id
        
        val updatedSession = session.copy(
            messages = updatedMessages,
            currentLeafMessageId = finalLeafId,
            updatedAt = clock.now()
        )
        
        _sessionState.value = UiState.Success(updatedSession)
        _currentBranchLeafId.value = finalLeafId
    }
    
    /**
     * Updates a specific message in the current session.
     * 
     * @param updatedMessage The updated message
     */
    fun updateMessageInSession(updatedMessage: eu.torvian.chatbot.common.models.ChatMessage) {
        val session = currentSession ?: return
        
        val updatedMessages = session.messages.map { message ->
            if (message.id == updatedMessage.id) updatedMessage else message
        }
        
        val updatedSession = session.copy(
            messages = updatedMessages,
            updatedAt = clock.now()
        )
        
        _sessionState.value = UiState.Success(updatedSession)
    }
    
    /**
     * Removes a message and its children from the current session.
     * 
     * @param messageId The ID of the message to remove
     */
    fun removeMessageFromSession(messageId: Long) {
        val session = currentSession ?: return
        
        // Find all messages to remove (the target message and all its descendants)
        val messagesToRemove = findMessageAndDescendants(session.messages, messageId)
        val updatedMessages = session.messages.filterNot { it.id in messagesToRemove }
        
        val updatedSession = session.copy(
            messages = updatedMessages,
            updatedAt = clock.now()
        )
        
        _sessionState.value = UiState.Success(updatedSession)
    }
    
    /**
     * Finds a message and all its descendants.
     * 
     * @param messages All messages in the session
     * @param messageId The ID of the root message to find descendants for
     * @return Set of message IDs including the root and all descendants
     */
    private fun findMessageAndDescendants(
        messages: List<eu.torvian.chatbot.common.models.ChatMessage>, 
        messageId: Long
    ): Set<Long> {
        val messageMap = messages.associateBy { it.id }
        val toRemove = mutableSetOf<Long>()
        val queue = mutableListOf(messageId)
        
        while (queue.isNotEmpty()) {
            val currentId = queue.removeFirst()
            if (toRemove.add(currentId)) {
                messageMap[currentId]?.childrenMessageIds?.let { children ->
                    queue.addAll(children)
                }
            }
        }
        
        return toRemove
    }
    
    /**
     * Retries loading the current session if it's in an error state.
     * 
     * @param onError Callback for handling errors during retry
     */
    fun retryLoadSession(onError: (ApiError) -> Unit = {}) {
        val currentState = _sessionState.value
        if (currentState is UiState.Error) {
            // We need to know which session to retry - this would need to be stored
            // For now, we'll just reset to idle state
            _sessionState.value = UiState.Idle
        }
    }
}
