package eu.torvian.chatbot.app.viewmodel.thread

import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.logging.kmpLogger

/**
 * Manages thread branch operations for chat message trees.
 * 
 * This class is responsible for:
 * - Building thread branches from flat message lists
 * - Finding leaf messages in branches
 * - Detecting and handling cycles in message trees
 * - Providing thread navigation utilities
 */
class ThreadBranchManager {
    
    private val logger = kmpLogger<ThreadBranchManager>()
    
    /**
     * Result of a thread branch operation.
     */
    sealed class ThreadBranchResult {
        data class Success(val messages: List<ChatMessage>) : ThreadBranchResult()
        data class Error(val message: String, val cause: ThreadBranchError) : ThreadBranchResult()
    }
    
    /**
     * Types of errors that can occur during thread branch operations.
     */
    enum class ThreadBranchError {
        LEAF_NOT_FOUND,
        CYCLE_DETECTED,
        BROKEN_LINK,
        EMPTY_MESSAGE_LIST,
        INVALID_MESSAGE_ID
    }
    
    /**
     * Builds the list of messages for a specific branch.
     * 
     * @param allMessages The flat list of all messages in the session
     * @param leafId The ID of the desired leaf message for the branch
     * @return ThreadBranchResult containing either the ordered messages or an error
     */
    fun buildThreadBranch(allMessages: List<ChatMessage>, leafId: Long?): ThreadBranchResult {
        if (leafId == null) {
            return ThreadBranchResult.Success(emptyList())
        }
        
        if (allMessages.isEmpty()) {
            return ThreadBranchResult.Error(
                "Cannot build branch: message list is empty",
                ThreadBranchError.EMPTY_MESSAGE_LIST
            )
        }
        
        val messageMap = allMessages.associateBy { it.id }
        val branch = mutableListOf<ChatMessage>()
        var currentMessageId: Long? = leafId
        val visitedIds = mutableSetOf<Long>()
        
        // Traverse upwards from the leaf to the root
        while (currentMessageId != null) {
            val message = messageMap[currentMessageId]
            if (message == null) {
                logger.error("Could not find message with ID $currentMessageId while building branch")
                return ThreadBranchResult.Error(
                    "Message with ID $currentMessageId not found",
                    ThreadBranchError.BROKEN_LINK
                )
            }
            
            if (!visitedIds.add(message.id)) {
                logger.error("Cycle detected in message thread path during upward traversal at message ID: ${message.id}")
                return ThreadBranchResult.Error(
                    "Cycle detected at message ID: ${message.id}",
                    ThreadBranchError.CYCLE_DETECTED
                )
            }
            
            branch.add(message)
            currentMessageId = message.parentMessageId
        }
        
        // Reverse to get root-to-leaf order
        return ThreadBranchResult.Success(branch.reversed())
    }
    
    /**
     * Finds the ultimate leaf message ID by traversing down the first child path from a given starting message ID.
     * 
     * @param startMessageId The ID of the message to start the traversal from
     * @param messageMap A map of all messages in the session for efficient lookup
     * @return The ID of the leaf message found, or null if the startMessageId is invalid or an error occurs
     */
    fun findLeafOfBranch(startMessageId: Long, messageMap: Map<Long, ChatMessage>): Long? {
        var currentMessage: ChatMessage? = messageMap[startMessageId]
        if (currentMessage == null) {
            logger.warn("Starting message for branch traversal not found: $startMessageId")
            return null
        }
        
        var finalLeafId = startMessageId
        val visitedIds = mutableSetOf<Long>()
        
        while (currentMessage?.childrenMessageIds?.isNotEmpty() == true) {
            if (!visitedIds.add(currentMessage.id)) {
                logger.error("Cycle detected in message thread path at message ID: ${currentMessage.id}")
                return null
            }
            
            // Select the first child to traverse down
            val firstChildId = currentMessage.childrenMessageIds.first()
            currentMessage = messageMap[firstChildId]
            if (currentMessage == null) {
                logger.warn("Child message $firstChildId not found during branch traversal. Using last valid message as leaf.")
                break
            }
            finalLeafId = currentMessage.id
        }
        
        return finalLeafId
    }
    
    /**
     * Finds all leaf messages in a message tree.
     * 
     * @param messages The list of all messages
     * @return List of message IDs that are leaf nodes (have no children)
     */
    fun findAllLeafMessages(messages: List<ChatMessage>): List<Long> {
        return messages
            .filter { it.childrenMessageIds.isEmpty() }
            .map { it.id }
    }
    
    /**
     * Finds all root messages in a message tree.
     * 
     * @param messages The list of all messages
     * @return List of message IDs that are root nodes (have no parent)
     */
    fun findAllRootMessages(messages: List<ChatMessage>): List<Long> {
        return messages
            .filter { it.parentMessageId == null }
            .map { it.id }
    }
    
    /**
     * Validates the integrity of a message tree structure.
     * 
     * @param messages The list of all messages to validate
     * @return List of validation errors found, empty if the tree is valid
     */
    fun validateMessageTree(messages: List<ChatMessage>): List<String> {
        val errors = mutableListOf<String>()
        val messageMap = messages.associateBy { it.id }
        
        for (message in messages) {
            // Check if parent exists (if specified)
            message.parentMessageId?.let { parentId ->
                if (messageMap[parentId] == null) {
                    errors.add("Message ${message.id} references non-existent parent $parentId")
                }
            }
            
            // Check if all children exist
            for (childId in message.childrenMessageIds) {
                if (messageMap[childId] == null) {
                    errors.add("Message ${message.id} references non-existent child $childId")
                }
            }
            
            // Check for self-reference
            if (message.parentMessageId == message.id) {
                errors.add("Message ${message.id} references itself as parent")
            }
            if (message.id in message.childrenMessageIds) {
                errors.add("Message ${message.id} references itself as child")
            }
        }
        
        // Check for orphaned messages (messages that claim to have a parent but the parent doesn't claim them as children)
        for (message in messages) {
            message.parentMessageId?.let { parentId ->
                val parent = messageMap[parentId]
                if (parent != null && message.id !in parent.childrenMessageIds) {
                    errors.add("Message ${message.id} claims parent $parentId, but parent doesn't list it as child")
                }
            }
        }
        
        return errors
    }
    
    /**
     * Gets the path from root to a specific message.
     * 
     * @param targetMessageId The ID of the target message
     * @param messageMap Map of all messages for efficient lookup
     * @return List of message IDs from root to target, or empty list if path cannot be determined
     */
    fun getPathToMessage(targetMessageId: Long, messageMap: Map<Long, ChatMessage>): List<Long> {
        val path = mutableListOf<Long>()
        var currentMessageId: Long? = targetMessageId
        val visitedIds = mutableSetOf<Long>()
        
        while (currentMessageId != null) {
            if (!visitedIds.add(currentMessageId)) {
                logger.error("Cycle detected while finding path to message $targetMessageId")
                return emptyList()
            }
            
            path.add(currentMessageId)
            val message = messageMap[currentMessageId]
            if (message == null) {
                logger.error("Message $currentMessageId not found while building path")
                return emptyList()
            }
            
            currentMessageId = message.parentMessageId
        }
        
        return path.reversed()
    }
    
    /**
     * Checks if one message is an ancestor of another.
     * 
     * @param ancestorId The potential ancestor message ID
     * @param descendantId The potential descendant message ID
     * @param messageMap Map of all messages for efficient lookup
     * @return True if ancestorId is an ancestor of descendantId
     */
    fun isAncestor(ancestorId: Long, descendantId: Long, messageMap: Map<Long, ChatMessage>): Boolean {
        if (ancestorId == descendantId) return false
        
        var currentMessageId: Long? = descendantId
        val visitedIds = mutableSetOf<Long>()
        
        while (currentMessageId != null) {
            if (!visitedIds.add(currentMessageId)) {
                return false // Cycle detected
            }
            
            val message = messageMap[currentMessageId] ?: return false
            currentMessageId = message.parentMessageId
            
            if (currentMessageId == ancestorId) {
                return true
            }
        }
        
        return false
    }
}
