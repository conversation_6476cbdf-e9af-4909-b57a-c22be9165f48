package eu.torvian.chatbot.app.viewmodel.state

import eu.torvian.chatbot.common.models.ChatMessage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Manages the state of message input and editing operations.
 * 
 * This class is responsible for:
 * - Managing the current input content
 * - Handling reply target state
 * - Managing message editing state
 * - Tracking sending state
 */
class MessageInputStateManager {
    
    private val _inputContent = MutableStateFlow("")
    
    /**
     * The current text content in the message input field.
     */
    val inputContent: StateFlow<String> = _inputContent.asStateFlow()
    
    private val _replyTargetMessage = MutableStateFlow<ChatMessage?>(null)
    
    /**
     * The message the user is currently explicitly replying to via the Reply action.
     * Null if not replying to any specific message.
     */
    val replyTargetMessage: StateFlow<ChatMessage?> = _replyTargetMessage.asStateFlow()
    
    private val _editingMessage = MutableStateFlow<ChatMessage?>(null)
    
    /**
     * The message currently being edited. Null if no message is being edited.
     */
    val editingMessage: StateFlow<ChatMessage?> = _editingMessage.asStateFlow()
    
    private val _editingContent = MutableStateFlow("")
    
    /**
     * The content of the message currently being edited.
     */
    val editingContent: StateFlow<String> = _editingContent.asStateFlow()
    
    private val _isSendingMessage = MutableStateFlow(false)
    
    /**
     * Indicates whether a message is currently in the process of being sent.
     */
    val isSendingMessage: StateFlow<Boolean> = _isSendingMessage.asStateFlow()
    
    /**
     * Updates the input content.
     * 
     * @param newText The new text content for the input field
     */
    fun updateInputContent(newText: String) {
        _inputContent.value = newText
    }
    
    /**
     * Gets the current input content trimmed of whitespace.
     * 
     * @return The trimmed input content
     */
    fun getTrimmedInputContent(): String {
        return _inputContent.value.trim()
    }
    
    /**
     * Checks if the current input content is valid (not blank after trimming).
     * 
     * @return True if input is valid, false otherwise
     */
    fun isInputContentValid(): Boolean {
        return getTrimmedInputContent().isNotBlank()
    }
    
    /**
     * Clears the input content.
     */
    fun clearInputContent() {
        _inputContent.value = ""
    }
    
    /**
     * Sets the reply target message.
     * 
     * @param message The message to reply to
     */
    fun setReplyTarget(message: ChatMessage) {
        _replyTargetMessage.value = message
    }
    
    /**
     * Clears the reply target.
     */
    fun clearReplyTarget() {
        _replyTargetMessage.value = null
    }
    
    /**
     * Gets the parent message ID for a new message.
     * This will be the reply target ID if set, otherwise null.
     * 
     * @param fallbackLeafId The fallback leaf ID to use if no reply target is set
     * @return The parent message ID to use
     */
    fun getParentMessageId(fallbackLeafId: Long?): Long? {
        return _replyTargetMessage.value?.id ?: fallbackLeafId
    }
    
    /**
     * Starts editing a message.
     * 
     * @param message The message to edit
     */
    fun startEditing(message: ChatMessage) {
        _editingMessage.value = message
        _editingContent.value = message.content
    }
    
    /**
     * Updates the editing content.
     * 
     * @param newText The new text content for the editing field
     */
    fun updateEditingContent(newText: String) {
        _editingContent.value = newText
    }
    
    /**
     * Gets the current editing content trimmed of whitespace.
     * 
     * @return The trimmed editing content
     */
    fun getTrimmedEditingContent(): String {
        return _editingContent.value.trim()
    }
    
    /**
     * Checks if the current editing content is valid (not blank after trimming).
     * 
     * @return True if editing content is valid, false otherwise
     */
    fun isEditingContentValid(): Boolean {
        return getTrimmedEditingContent().isNotBlank()
    }
    
    /**
     * Cancels the current editing operation.
     */
    fun cancelEditing() {
        _editingMessage.value = null
        _editingContent.value = ""
    }
    
    /**
     * Completes the editing operation by clearing the editing state.
     */
    fun completeEditing() {
        _editingMessage.value = null
        _editingContent.value = ""
    }
    
    /**
     * Sets the sending state.
     * 
     * @param isSending True if currently sending a message, false otherwise
     */
    fun setSendingState(isSending: Boolean) {
        _isSendingMessage.value = isSending
    }
    
    /**
     * Completes a successful message send by clearing input and reply target.
     */
    fun completeMessageSend() {
        clearInputContent()
        clearReplyTarget()
    }
}
