package eu.torvian.chatbot.app.viewmodel.error

import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.api.apiRequestError
import eu.torvian.chatbot.common.logging.kmpLogger
import org.jetbrains.compose.resources.getString
import chatbot.app.generated.resources.Res
import chatbot.app.generated.resources.error_loading_session_short
import chatbot.app.generated.resources.error_sending_message_short
import chatbot.app.generated.resources.error_updating_message_short
import chatbot.app.generated.resources.error_deleting_message_short
import chatbot.app.generated.resources.error_switching_branch_short

/**
 * Centralized error handling for ChatViewModel operations.
 * 
 * This class provides consistent error handling patterns across all ChatViewModel operations,
 * including logging, user notification via EventBus, and error recovery strategies.
 */
class ChatViewModelErrorHandler(
    private val eventBus: EventBus
) {
    
    private val logger = kmpLogger<ChatViewModelErrorHandler>()
    
    /**
     * Handles errors that occur during session loading operations.
     * 
     * @param error The API error that occurred
     * @param sessionId The ID of the session that failed to load
     * @param operation Optional description of the specific operation that failed
     */
    suspend fun handleSessionLoadError(
        error: ApiError, 
        sessionId: Long, 
        operation: String = "load session"
    ) {
        logger.error("Session $operation error for session $sessionId: ${error.code} - ${error.message}")
        
        eventBus.emitEvent(
            apiRequestError(
                apiError = error,
                shortMessage = getString(Res.string.error_loading_session_short),
            )
        )
    }
    
    /**
     * Handles errors that occur during message sending operations.
     * 
     * @param error The API error that occurred
     * @param sessionId The ID of the session where the message failed to send
     * @param isStreaming Whether this was a streaming operation
     */
    suspend fun handleMessageSendError(
        error: ApiError, 
        sessionId: Long, 
        isStreaming: Boolean = false
    ) {
        val operationType = if (isStreaming) "Streaming" else "Send"
        logger.error("$operationType message API error for session $sessionId: ${error.code} - ${error.message}")
        
        eventBus.emitEvent(
            apiRequestError(
                apiError = error,
                shortMessage = getString(Res.string.error_sending_message_short),
            )
        )
    }
    
    /**
     * Handles errors that occur during message update operations.
     * 
     * @param error The API error that occurred
     * @param messageId The ID of the message that failed to update
     */
    suspend fun handleMessageUpdateError(error: ApiError, messageId: Long) {
        logger.error("Update message API error for message $messageId: ${error.code} - ${error.message}")
        
        eventBus.emitEvent(
            apiRequestError(
                apiError = error,
                shortMessage = getString(Res.string.error_updating_message_short),
            )
        )
    }
    
    /**
     * Handles errors that occur during message deletion operations.
     * 
     * @param error The API error that occurred
     * @param messageId The ID of the message that failed to delete
     */
    suspend fun handleMessageDeleteError(error: ApiError, messageId: Long) {
        logger.error("Delete message API error for message $messageId: ${error.code} - ${error.message}")
        
        eventBus.emitEvent(
            apiRequestError(
                apiError = error,
                shortMessage = getString(Res.string.error_deleting_message_short),
            )
        )
    }
    
    /**
     * Handles errors that occur during branch switching operations.
     * 
     * @param error The API error that occurred
     * @param sessionId The ID of the session where branch switching failed
     * @param targetMessageId The ID of the message that was the target of the branch switch
     */
    suspend fun handleBranchSwitchError(
        error: ApiError, 
        sessionId: Long, 
        targetMessageId: Long
    ) {
        logger.error("Switch branch API error for session $sessionId, target message $targetMessageId: ${error.code} - ${error.message}")
        
        eventBus.emitEvent(
            apiRequestError(
                apiError = error,
                shortMessage = getString(Res.string.error_switching_branch_short),
            )
        )
    }
    
    /**
     * Handles streaming-specific errors that occur during message streaming.
     * 
     * @param error The API error that occurred during streaming
     * @param sessionId The ID of the session where streaming failed
     * @param streamingPhase Description of which phase of streaming failed
     */
    suspend fun handleStreamingError(
        error: ApiError, 
        sessionId: Long, 
        streamingPhase: String = "unknown"
    ) {
        logger.error("Streaming error in phase '$streamingPhase' for session $sessionId: ${error.code} - ${error.message}")
        
        eventBus.emitEvent(
            apiRequestError(
                apiError = error,
                shortMessage = getString(Res.string.error_sending_message_short),
            )
        )
    }
    
    /**
     * Handles validation errors that occur before API calls.
     * These are typically client-side validation failures.
     * 
     * @param validationMessage The validation error message
     * @param operation The operation that failed validation
     */
    fun handleValidationError(validationMessage: String, operation: String) {
        logger.warn("Validation error in $operation: $validationMessage")
        // For validation errors, we typically don't emit to EventBus as they're user input issues
        // The UI should handle these directly
    }
    
    /**
     * Handles data consistency errors that occur during thread operations.
     * These indicate potential issues with the message tree structure.
     * 
     * @param errorMessage The error message describing the consistency issue
     * @param sessionId The ID of the session where the issue occurred
     * @param messageId The ID of the message involved in the issue (if applicable)
     */
    fun handleDataConsistencyError(
        errorMessage: String, 
        sessionId: Long, 
        messageId: Long? = null
    ) {
        val messageInfo = messageId?.let { " (message $it)" } ?: ""
        logger.error("Data consistency error in session $sessionId$messageInfo: $errorMessage")
        
        // Data consistency errors are serious and should be logged but may not need user notification
        // unless they prevent normal operation
    }
    
    /**
     * Handles general unexpected errors that don't fit other categories.
     * 
     * @param error The throwable that occurred
     * @param operation The operation that was being performed
     * @param context Additional context information
     */
    fun handleUnexpectedError(
        error: Throwable, 
        operation: String, 
        context: String = ""
    ) {
        val contextInfo = if (context.isNotEmpty()) " ($context)" else ""
        logger.error("Unexpected error during $operation$contextInfo", error)
        
        // For unexpected errors, we might want to emit a generic error event
        // but we need to be careful not to expose internal details to users
    }
}
