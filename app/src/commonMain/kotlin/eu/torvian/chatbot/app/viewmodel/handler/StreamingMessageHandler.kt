package eu.torvian.chatbot.app.viewmodel.handler

import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.viewmodel.error.ChatViewModelErrorHandler
import eu.torvian.chatbot.app.viewmodel.state.SessionStateManager
import eu.torvian.chatbot.app.viewmodel.state.MessageInputStateManager
import eu.torvian.chatbot.app.viewmodel.state.StreamingStateManager
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ChatStreamEvent
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.logging.kmpLogger
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

/**
 * Handles streaming message processing operations.
 * 
 * This class is responsible for:
 * - Processing new messages with streaming responses
 * - Managing streaming state during the process
 * - Coordinating with state managers to update UI state
 * - Handling streaming-specific errors and events
 */
class StreamingMessageHandler(
    private val chatApi: ChatApi,
    private val sessionStateManager: SessionStateManager,
    private val inputStateManager: MessageInputStateManager,
    private val streamingStateManager: StreamingStateManager,
    private val errorHandler: ChatViewModelErrorHandler,
    private val scope: CoroutineScope,
    private val dispatcher: CoroutineDispatcher
) {
    
    private val logger = kmpLogger<StreamingMessageHandler>()
    
    /**
     * Processes a new message with streaming response.
     * 
     * @param session The current chat session
     * @param content The message content to send
     * @param parentId The parent message ID (optional)
     */
    fun processStreamingMessage(session: ChatSession, content: String, parentId: Long?) {
        scope.launch(dispatcher) {
            inputStateManager.setSendingState(true)
            
            try {
                // Clear any previous streaming state
                streamingStateManager.clearAllStreamingState()
                
                chatApi.processNewMessageStreaming(
                    sessionId = session.id,
                    request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
                ).collect { eitherUpdate ->
                    eitherUpdate.fold(
                        ifLeft = { error ->
                            handleStreamingError(error, session.id)
                        },
                        ifRight = { chatUpdate ->
                            handleStreamingEvent(chatUpdate, session)
                        }
                    )
                }
            } finally {
                inputStateManager.setSendingState(false)
            }
        }
    }
    
    /**
     * Handles individual streaming events.
     * 
     * @param event The streaming event to handle
     * @param session The current chat session
     */
    private suspend fun handleStreamingEvent(event: ChatStreamEvent, session: ChatSession) {
        when (event) {
            is ChatStreamEvent.UserMessageSaved -> {
                handleUserMessageSaved(event)
            }
            
            is ChatStreamEvent.AssistantMessageStart -> {
                handleAssistantMessageStart(event)
            }
            
            is ChatStreamEvent.AssistantMessageDelta -> {
                handleAssistantMessageDelta(event)
            }
            
            is ChatStreamEvent.AssistantMessageEnd -> {
                handleAssistantMessageEnd(event, session)
            }
            
            is ChatStreamEvent.ErrorOccurred -> {
                handleStreamingEventError(event, session.id)
            }
            
            ChatStreamEvent.StreamCompleted -> {
                handleStreamCompleted(session.id)
            }
        }
    }
    
    /**
     * Handles the user message saved event.
     */
    private fun handleUserMessageSaved(event: ChatStreamEvent.UserMessageSaved) {
        logger.debug("User message saved: ${event.message.id}")
        
        // Store the user message in the temporary streaming state
        streamingStateManager.setStreamingUserMessage(event.message)
        sessionStateManager.updateCurrentBranchLeafId(event.message.id)
        
        // Clear input and reply target after user message is confirmed
        inputStateManager.completeMessageSend()
    }
    
    /**
     * Handles the assistant message start event.
     */
    private fun handleAssistantMessageStart(event: ChatStreamEvent.AssistantMessageStart) {
        logger.debug("Assistant message started: ${event.assistantMessage.id}")
        
        // Use the assistant message directly from the update
        sessionStateManager.updateCurrentBranchLeafId(event.assistantMessage.id)
        streamingStateManager.setStreamingAssistantMessage(event.assistantMessage)
    }
    
    /**
     * Handles assistant message delta events (incremental content updates).
     */
    private fun handleAssistantMessageDelta(event: ChatStreamEvent.AssistantMessageDelta) {
        logger.debug("Assistant message delta: '${event.deltaContent}'")
        
        // Update the streaming message content
        streamingStateManager.updateStreamingAssistantContent(event.deltaContent)
    }
    
    /**
     * Handles the assistant message end event.
     */
    private suspend fun handleAssistantMessageEnd(event: ChatStreamEvent.AssistantMessageEnd, session: ChatSession) {
        logger.debug("Assistant message ended: ${event.finalAssistantMessage.id}")
        
        // Update the session with the new messages
        val newMessages = listOf(event.finalUserMessage, event.finalAssistantMessage)
        val newLeafId = event.finalAssistantMessage.id
        
        sessionStateManager.addMessagesToSession(newMessages, newLeafId)
        
        // Clear streaming state
        streamingStateManager.clearAllStreamingState()
    }
    
    /**
     * Handles streaming-specific error events.
     */
    private suspend fun handleStreamingEventError(event: ChatStreamEvent.ErrorOccurred, sessionId: Long) {
        logger.error("Streaming event error: ${event.error.message}")
        
        // Clear streaming state on error
        streamingStateManager.clearAllStreamingState()
        
        // Handle the error through the error handler
        errorHandler.handleStreamingError(event.error, sessionId, "event processing")
    }
    
    /**
     * Handles stream completion.
     */
    private fun handleStreamCompleted(sessionId: Long) {
        logger.info("Streaming completed for session $sessionId")
        // Stream completion is handled by AssistantMessageEnd, so no additional action needed
    }
    
    /**
     * Handles errors that occur during streaming setup or collection.
     */
    private suspend fun handleStreamingError(error: eu.torvian.chatbot.common.api.ApiError, sessionId: Long) {
        logger.error("Streaming setup error: ${error.code} - ${error.message}")
        
        // Clear any streaming state and emit error
        streamingStateManager.clearAllStreamingState()
        
        errorHandler.handleMessageSendError(error, sessionId, isStreaming = true)
    }
    
    /**
     * Cancels any active streaming operation.
     * This can be called when the user wants to stop streaming or when switching sessions.
     */
    fun cancelStreaming() {
        logger.info("Cancelling active streaming operation")
        streamingStateManager.clearAllStreamingState()
        inputStateManager.setSendingState(false)
    }
    
    /**
     * Checks if streaming is currently active.
     * 
     * @return True if streaming is active, false otherwise
     */
    fun isStreamingActive(): Boolean {
        return streamingStateManager.isStreaming
    }
}
