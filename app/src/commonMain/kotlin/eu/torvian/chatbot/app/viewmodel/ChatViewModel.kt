package eu.torvian.chatbot.app.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.app.service.api.SettingsApi
import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.app.viewmodel.error.ChatViewModelErrorHandler
import eu.torvian.chatbot.app.viewmodel.handler.MessageProcessingHandler
import eu.torvian.chatbot.app.viewmodel.handler.StreamingMessageHandler
import eu.torvian.chatbot.app.viewmodel.state.DerivedStateManager
import eu.torvian.chatbot.app.viewmodel.state.MessageInputStateManager
import eu.torvian.chatbot.app.viewmodel.state.SessionStateManager
import eu.torvian.chatbot.app.viewmodel.state.StreamingStateManager
import eu.torvian.chatbot.app.viewmodel.thread.ThreadBranchManager
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.*
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

/**
 * Manages the UI state for the main chat area of the currently active session.
 *
 * This refactored version uses a modular architecture with separate managers for different concerns:
 * - SessionStateManager: Handles session loading and state management
 * - MessageInputStateManager: Manages input, editing, and reply state
 * - StreamingStateManager: Handles streaming message state
 * - ThreadBranchManager: Manages thread branch operations
 * - MessageProcessingHandler: Handles non-streaming message operations
 * - StreamingMessageHandler: Handles streaming message operations
 * - ChatViewModelErrorHandler: Centralized error handling
 * - DerivedStateManager: Manages computed state for UI consumption
 *
 * @constructor
 * @param sessionApi The API client for session-related operations.
 * @param chatApi The API client for chat message-related operations.
 * @param settingsApi The API client for model settings.
 * @param eventBus The event bus for emitting global events like retry-able errors.
 * @param uiDispatcher The dispatcher to use for UI-related coroutines. Defaults to Main.
 * @param clock The clock to use for timestamping. Defaults to System clock.
 *
 * @property sessionState The state of the currently loaded chat session, including all messages.
 * @property currentBranchLeafId The ID of the leaf message in the currently displayed thread branch.
 * @property displayedMessages The list of messages to display in the UI, representing the currently selected thread branch.
 * @property inputContent The current text content in the message input field.
 * @property replyTargetMessage The message the user is currently explicitly replying to via the Reply action.
 * @property editingMessage The message currently being edited (E3.S1, E3.S2).
 * @property editingContent The content of the message currently being edited (E3.S1, E3.S2).
 */
class ChatViewModel(
    private val sessionApi: SessionApi,
    private val chatApi: ChatApi,
    private val settingsApi: SettingsApi,
    private val eventBus: EventBus,
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main,
    private val clock: Clock = Clock.System
) : ViewModel() {

    // --- Component Initialization ---

    private val errorHandler = ChatViewModelErrorHandler(eventBus)

    private val sessionStateManager = SessionStateManager(
        sessionApi = sessionApi,
        scope = viewModelScope,
        dispatcher = uiDispatcher,
        clock = clock
    )

    private val inputStateManager = MessageInputStateManager()

    private val streamingStateManager = StreamingStateManager()

    private val threadBranchManager = ThreadBranchManager()

    private val derivedStateManager = DerivedStateManager(
        sessionStateManager = sessionStateManager,
        streamingStateManager = streamingStateManager,
        threadBranchManager = threadBranchManager
    )

    private val messageProcessingHandler = MessageProcessingHandler(
        chatApi = chatApi,
        sessionStateManager = sessionStateManager,
        inputStateManager = inputStateManager,
        errorHandler = errorHandler,
        scope = viewModelScope,
        dispatcher = uiDispatcher
    )

    private val streamingMessageHandler = StreamingMessageHandler(
        chatApi = chatApi,
        sessionStateManager = sessionStateManager,
        inputStateManager = inputStateManager,
        streamingStateManager = streamingStateManager,
        errorHandler = errorHandler,
        scope = viewModelScope,
        dispatcher = uiDispatcher
    )

    // --- Public State Properties (Delegated to Managers) ---

    /**
     * The state of the currently loaded chat session, including all messages.
     * When in Success state, provides the ChatSession object.
     */
    val sessionState: StateFlow<UiState<ApiError, ChatSession>> = sessionStateManager.sessionState

    /**
     * The ID of the leaf message in the currently displayed thread branch.
     * Changing this triggers the UI to show a different branch.
     * Null if the session is empty or not loaded/successful.
     */
    val currentBranchLeafId: StateFlow<Long?> = sessionStateManager.currentBranchLeafId

    /**
     * The list of messages to display in the UI, representing the currently selected thread branch.
     * This is derived from the session's full list of messages and the current leaf message ID,
     * combined with any actively streaming message.
     */
    val displayedMessages: StateFlow<List<ChatMessage>> = derivedStateManager.displayedMessages

    /**
     * The current text content in the message input field.
     */
    val inputContent: StateFlow<String> = inputStateManager.inputContent

    /**
     * The message the user is currently explicitly replying to via the Reply action (E1.S7).
     * If null, sending a message replies to the [currentBranchLeafId] value.
     */
    val replyTargetMessage: StateFlow<ChatMessage?> = inputStateManager.replyTargetMessage

    /**
     * The message currently being edited (E3.S1, E3.S2). Null if no message is being edited.
     */
    val editingMessage: StateFlow<ChatMessage?> = inputStateManager.editingMessage

    /**
     * The content of the message currently being edited (E3.S1, E3.S2).
     */
    val editingContent: StateFlow<String> = inputStateManager.editingContent

    /**
     * Indicates whether a message is currently in the process of being sent. (E1.S3)
     */
    val isSendingMessage: StateFlow<Boolean> = inputStateManager.isSendingMessage



    // --- Public Action Functions (Called by UI Components) ---

    /**
     * Loads a chat session and its messages by ID.
     * Triggered when a session is selected in the session list (E2.S4).
     *
     * @param sessionId The ID of the session to load, or null to clear the session.
     * @param forceReload If true, reloads the session even if it's already loaded successfully.
     */
    fun loadSession(sessionId: Long?, forceReload: Boolean = false) {
        if (sessionId == null) {
            clearSession()
            return
        }

        // Prevent reloading if already loading or if the session is already loaded successfully
        val currentState = sessionStateManager.sessionState.value
        if (!forceReload && (currentState.isLoading || (currentState.dataOrNull?.id == sessionId))) return

        // Clear input and editing state when loading a new session
        inputStateManager.clearInputContent()
        inputStateManager.clearReplyTarget()
        inputStateManager.cancelEditing()
        streamingMessageHandler.cancelStreaming()

        sessionStateManager.loadSession(sessionId) { error ->
            viewModelScope.launch(uiDispatcher) {
                errorHandler.handleSessionLoadError(error, sessionId)
            }
        }
    }

    /**
     * Clears the currently loaded session state.
     * Called when the selected session is deleted or potentially on app exit.
     */
    fun clearSession() {
        inputStateManager.clearInputContent()
        inputStateManager.clearReplyTarget()
        inputStateManager.cancelEditing()
        streamingMessageHandler.cancelStreaming()
        // Note: We don't directly clear session state here as it's managed by SessionStateManager
        // The UI should handle transitioning to idle state when no session is selected
    }

    /**
     * Updates the content of the message input field.
     *
     * @param newText The new text from the input field.
     */
    fun updateInput(newText: String) {
        inputStateManager.updateInputContent(newText)
    }

    /**
     * Sends the current message content to the active session.
     * Determines the parent based on [replyTargetMessage] or [currentBranchLeafId].
     * (E1.S1, E1.S7)
     */
    fun sendMessage() {
        val currentSession = sessionStateManager.currentSession ?: return
        if (!inputStateManager.isInputContentValid()) return

        val content = inputStateManager.getTrimmedInputContent()
        val parentId = inputStateManager.getParentMessageId(sessionStateManager.currentBranchLeafId.value)

        // Check if streaming is enabled in settings
        val isStreamingEnabled = true // TODO: Implement settings check

        if (isStreamingEnabled) {
            streamingMessageHandler.processStreamingMessage(currentSession, content, parentId)
        } else {
            messageProcessingHandler.processNewMessage(currentSession, content, parentId)
        }
    }



    /**
     * Sets the state to indicate the user is replying to a specific message (E1.S7).
     *
     * @param message The message to reply to.
     */
    fun startReplyTo(message: ChatMessage) {
        inputStateManager.setReplyTarget(message)
    }

    /**
     * Cancels the specific reply target, reverting to replying to the current leaf (E1.S7).
     */
    fun cancelReply() {
        inputStateManager.clearReplyTarget()
    }

    /**
     * Sets the state to indicate a message is being edited (E3.S1, E3.S2).
     *
     * @param message The message to edit.
     */
    fun startEditing(message: ChatMessage) {
        inputStateManager.startEditing(message)
    }

    /**
     * Updates the content of the message currently being edited (E3.S1, E3.S2).
     * Called by the UI as the user types in the editing input field.
     *
     * @param newText The new text content for the editing field.
     */
    fun updateEditingContent(newText: String) {
        inputStateManager.updateEditingContent(newText)
    }

    /**
     * Saves the edited message content (E3.S3).
     */
    fun saveEditing() {
        val messageToEdit = inputStateManager.editingMessage.value ?: return
        if (!inputStateManager.isEditingContentValid()) {
            errorHandler.handleValidationError("Message content cannot be empty", "save editing")
            return
        }

        val newContent = inputStateManager.getTrimmedEditingContent()
        messageProcessingHandler.updateMessageContent(
            messageId = messageToEdit.id,
            newContent = newContent,
            onSuccess = { inputStateManager.completeEditing() },
            onError = { /* Error already handled by messageProcessingHandler */ }
        )
    }

    /**
     * Cancels the message editing state (E3.S1, E3.S2).
     */
    fun cancelEditing() {
        inputStateManager.cancelEditing()
    }

    /**
     * Deletes a specific message and its children from the session (E3.S4).
     *
     * @param messageId The ID of the message to delete.
     */
    fun deleteMessage(messageId: Long) {
        messageProcessingHandler.deleteMessage(
            messageId = messageId,
            onSuccess = {
                // Reload the session to update the UI state correctly
                sessionStateManager.currentSession?.let { session ->
                    loadSession(session.id, forceReload = true)
                }
            },
            onError = { /* Error already handled by messageProcessingHandler */ }
        )
    }

    /**
     * Switches the currently displayed chat branch to the one that includes the given message ID.
     * The ViewModel will find the actual leaf message of this branch by traversing down
     * the path of first children starting from the provided `targetMessageId`.
     * This new leaf message ID is then persisted to the session record (E1.S5).
     *
     * @param targetMessageId The ID of the message that serves as the starting point for
     *                        determining the new displayed branch. This message itself may be
     *                        a root, middle, or leaf message in the conversation tree.
     */
    fun switchBranchToMessage(targetMessageId: Long) {
        val currentSession = sessionStateManager.currentSession ?: return
        if (sessionStateManager.currentBranchLeafId.value == targetMessageId) return

        val messageMap = currentSession.messages.associateBy { it.id }

        // Use the thread branch manager to find the actual leaf ID
        val finalLeafId = threadBranchManager.findLeafOfBranch(targetMessageId, messageMap)
        if (finalLeafId == null) {
            errorHandler.handleDataConsistencyError(
                "Could not determine a valid leaf for branch starting with $targetMessageId",
                currentSession.id,
                targetMessageId
            )
            return
        }

        if (sessionStateManager.currentBranchLeafId.value == finalLeafId) return // Already on this exact branch

        sessionStateManager.updateSessionLeafMessage(
            leafId = finalLeafId,
            onError = { error ->
                viewModelScope.launch(uiDispatcher) {
                    errorHandler.handleBranchSwitchError(error, currentSession.id, targetMessageId)
                }
            }
        )
    }

    /**
     * Sets the selected model for the current session (E4.S7).
     *
     * @param modelId The ID of the model to select, or null to unset.
     */
    fun selectModel(modelId: Long?) {
        val currentSession = sessionStateManager.currentSession ?: return
        viewModelScope.launch(uiDispatcher) {
            sessionApi.updateSessionModel(currentSession.id, UpdateSessionModelRequest(modelId))
                .fold(
                    ifLeft = { error ->
                        errorHandler.handleSessionLoadError(error, currentSession.id, "update session model")
                    },
                    ifRight = {
                        // Update the session object within the state Flow manually as backend doesn't return it
                        val updatedSession = currentSession.copy(currentModelId = modelId)
                        sessionStateManager.updateSession(updatedSession)
                    }
                )
        }
    }

    /**
     * Sets the selected settings profile for the current session (E4.S7).
     *
     * @param settingsId The ID of the settings profile to select, or null to unset.
     */
    fun selectSettings(settingsId: Long?) {
        val currentSession = sessionStateManager.currentSession ?: return
        viewModelScope.launch(uiDispatcher) {
            sessionApi.updateSessionSettings(currentSession.id, UpdateSessionSettingsRequest(settingsId))
                .fold(
                    ifLeft = { error ->
                        errorHandler.handleSessionLoadError(error, currentSession.id, "update session settings")
                    },
                    ifRight = {
                        // Update the session object within the state Flow manually
                        val updatedSession = currentSession.copy(currentSettingsId = settingsId)
                        sessionStateManager.updateSession(updatedSession)
                    }
                )
        }
    }

    // Note: Copy Message (E3.S5) and Copy Branch (E2.S7) methods are UI-only and don't involve API calls,
    // so they would remain similar, operating on the displayedMessages StateFlow value.
    // Example:
    /*
    fun copyMessageContent(message: ChatMessage) {
       // Use ClipboardManager from Compose.current.clipboardManager
       // clipboardManager.setText(AnnotatedString(message.content))
    }

    fun copyVisibleBranchContent() {
       // Format displayedMessages.value into a single string
       // Use ClipboardManager to set the text
    }
    */
}