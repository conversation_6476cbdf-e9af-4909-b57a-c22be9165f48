package eu.torvian.chatbot.app.viewmodel.handler

import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.viewmodel.error.ChatViewModelErrorHandler
import eu.torvian.chatbot.app.viewmodel.state.SessionStateManager
import eu.torvian.chatbot.app.viewmodel.state.MessageInputStateManager
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Handles non-streaming message processing operations.
 * 
 * This class is responsible for:
 * - Processing new messages without streaming
 * - Coordinating with state managers to update UI state
 * - Handling errors during message processing
 */
class MessageProcessingHandler(
    private val chatApi: ChatApi,
    private val sessionStateManager: SessionStateManager,
    private val inputStateManager: MessageInputStateManager,
    private val errorHandler: ChatViewModelErrorHandler,
    private val scope: CoroutineScope,
    private val dispatcher: CoroutineDispatcher
) {
    
    /**
     * Processes a new message without streaming.
     * 
     * @param session The current chat session
     * @param content The message content to send
     * @param parentId The parent message ID (optional)
     */
    fun processNewMessage(session: ChatSession, content: String, parentId: Long?) {
        scope.launch(dispatcher) {
            inputStateManager.setSendingState(true)
            
            try {
                chatApi.processNewMessage(
                    sessionId = session.id,
                    request = ProcessNewMessageRequest(content = content, parentMessageId = parentId)
                ).fold(
                    ifLeft = { error ->
                        errorHandler.handleMessageSendError(error, session.id, isStreaming = false)
                    },
                    ifRight = { newMessages ->
                        // Add the new messages to the current session
                        val newLeafId = newMessages.lastOrNull()?.id
                        sessionStateManager.addMessagesToSession(newMessages, newLeafId)
                        
                        // Update the current branch leaf ID
                        newLeafId?.let { sessionStateManager.updateCurrentBranchLeafId(it) }
                        
                        // Clear input and reply target on success
                        inputStateManager.completeMessageSend()
                    }
                )
            } finally {
                inputStateManager.setSendingState(false)
            }
        }
    }
    
    /**
     * Updates the content of an existing message.
     * 
     * @param messageId The ID of the message to update
     * @param newContent The new content for the message
     * @param onSuccess Callback for successful update
     * @param onError Callback for handling errors
     */
    fun updateMessageContent(
        messageId: Long,
        newContent: String,
        onSuccess: () -> Unit = {},
        onError: () -> Unit = {}
    ) {
        scope.launch(dispatcher) {
            chatApi.updateMessageContent(
                messageId = messageId,
                request = eu.torvian.chatbot.common.models.UpdateMessageRequest(content = newContent)
            ).fold(
                ifLeft = { error ->
                    errorHandler.handleMessageUpdateError(error, messageId)
                    onError()
                },
                ifRight = { updatedMessage ->
                    sessionStateManager.updateMessageInSession(updatedMessage)
                    onSuccess()
                }
            )
        }
    }
    
    /**
     * Deletes a message and its children.
     * 
     * @param messageId The ID of the message to delete
     * @param onSuccess Callback for successful deletion
     * @param onError Callback for handling errors
     */
    fun deleteMessage(
        messageId: Long,
        onSuccess: () -> Unit = {},
        onError: () -> Unit = {}
    ) {
        scope.launch(dispatcher) {
            chatApi.deleteMessage(messageId).fold(
                ifLeft = { error ->
                    errorHandler.handleMessageDeleteError(error, messageId)
                    onError()
                },
                ifRight = {
                    sessionStateManager.removeMessageFromSession(messageId)
                    onSuccess()
                }
            )
        }
    }
}
