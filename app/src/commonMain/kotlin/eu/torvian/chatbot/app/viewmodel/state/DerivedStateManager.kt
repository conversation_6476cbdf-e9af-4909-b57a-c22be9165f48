package eu.torvian.chatbot.app.viewmodel.state

import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.viewmodel.thread.ThreadBranchManager
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatSession
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.map

/**
 * Manages derived state calculations for the ChatViewModel.
 * 
 * This class is responsible for:
 * - Creating derived StateFlows from base state
 * - Optimizing state calculations to avoid redundant work
 * - Providing computed state for UI consumption
 * - Managing complex state combinations
 */
class DerivedStateManager(
    private val sessionStateManager: SessionStateManager,
    private val streamingStateManager: StreamingStateManager,
    private val threadBranchManager: ThreadBranchManager
) {
    
    /**
     * The list of messages to display in the UI, representing the currently selected thread branch.
     * This is derived from the session's full list of messages and the current leaf message ID,
     * combined with any actively streaming message.
     */
    val displayedMessages: StateFlow<List<ChatMessage>> = combine(
        sessionStateManager.sessionState.filterIsInstance<UiState.Success<ChatSession>>()
            .map { it.data.messages },
        sessionStateManager.currentBranchLeafId,
        streamingStateManager.streamingUserMessage,
        streamingStateManager.streamingAssistantMessage
    ) { allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage ->
        buildDisplayedMessages(allPersistedMessages, leafId, streamingUserMessage, streamingAssistantMessage)
    }
    
    /**
     * Indicates whether the UI should show a loading state for the session.
     */
    val isSessionLoading: StateFlow<Boolean> = sessionStateManager.sessionState.map { state ->
        state is UiState.Loading
    }
    
    /**
     * Indicates whether the session is in an error state.
     */
    val isSessionError: StateFlow<Boolean> = sessionStateManager.sessionState.map { state ->
        state is UiState.Error
    }
    
    /**
     * Gets the current session error if in error state.
     */
    val sessionError: StateFlow<ApiError?> = sessionStateManager.sessionState.map { state ->
        (state as? UiState.Error)?.error
    }
    
    /**
     * Indicates whether the session has been successfully loaded.
     */
    val isSessionLoaded: StateFlow<Boolean> = sessionStateManager.sessionState.map { state ->
        state is UiState.Success
    }
    
    /**
     * Gets the current session data if available.
     */
    val currentSessionData: StateFlow<ChatSession?> = sessionStateManager.sessionState.map { state ->
        (state as? UiState.Success)?.data
    }
    
    /**
     * Indicates whether there are any messages to display.
     */
    val hasMessages: StateFlow<Boolean> = displayedMessages.map { messages ->
        messages.isNotEmpty()
    }
    
    /**
     * Gets the count of displayed messages.
     */
    val messageCount: StateFlow<Int> = displayedMessages.map { messages ->
        messages.size
    }
    
    /**
     * Indicates whether streaming is currently active.
     */
    val isStreaming: StateFlow<Boolean> = combine(
        streamingStateManager.streamingUserMessage,
        streamingStateManager.streamingAssistantMessage
    ) { userMessage, assistantMessage ->
        userMessage != null || assistantMessage != null
    }
    
    /**
     * Gets the current streaming content for display purposes.
     */
    val currentStreamingContent: StateFlow<String> = streamingStateManager.streamingAssistantMessage.map { message ->
        message?.content ?: ""
    }
    
    /**
     * Builds the list of messages to display, combining persisted messages with streaming messages.
     * 
     * @param persistedMessages The persisted messages from the session
     * @param leafId The current branch leaf ID
     * @param streamingUserMessage The currently streaming user message (if any)
     * @param streamingAssistantMessage The currently streaming assistant message (if any)
     * @return The ordered list of messages to display
     */
    private fun buildDisplayedMessages(
        persistedMessages: List<ChatMessage>,
        leafId: Long?,
        streamingUserMessage: ChatMessage.UserMessage?,
        streamingAssistantMessage: ChatMessage.AssistantMessage?
    ): List<ChatMessage> {
        // Combine persisted messages with streaming messages
        val allMessages = persistedMessages + listOfNotNull(streamingUserMessage, streamingAssistantMessage)
        
        // If no leaf ID is specified, return empty list
        if (leafId == null) return emptyList()
        
        // Build the thread branch using the thread manager
        return when (val result = threadBranchManager.buildThreadBranch(allMessages, leafId)) {
            is ThreadBranchManager.ThreadBranchResult.Success -> result.messages
            is ThreadBranchManager.ThreadBranchResult.Error -> {
                // Log the error but return empty list to avoid UI crashes
                // The error is already logged by ThreadBranchManager
                emptyList()
            }
        }
    }
    
    /**
     * Creates a derived state that combines multiple boolean states with AND logic.
     * 
     * @param states The StateFlows to combine
     * @return A StateFlow that emits true only when all input states are true
     */
    fun combineWithAnd(vararg states: StateFlow<Boolean>): StateFlow<Boolean> {
        return combine(*states) { values ->
            values.all { it }
        }
    }
    
    /**
     * Creates a derived state that combines multiple boolean states with OR logic.
     * 
     * @param states The StateFlows to combine
     * @return A StateFlow that emits true when any input state is true
     */
    fun combineWithOr(vararg states: StateFlow<Boolean>): StateFlow<Boolean> {
        return combine(*states) { values ->
            values.any { it }
        }
    }
    
    /**
     * Creates a derived state that indicates whether any operation is in progress.
     * This includes session loading, message sending, and streaming.
     */
    val isAnyOperationInProgress: StateFlow<Boolean> = combineWithOr(
        isSessionLoading,
        isStreaming
    )
    
    /**
     * Creates a derived state that indicates whether the UI should be interactive.
     * The UI is interactive when a session is loaded and no critical operations are in progress.
     */
    val isUiInteractive: StateFlow<Boolean> = combine(
        isSessionLoaded,
        isAnyOperationInProgress
    ) { sessionLoaded, operationInProgress ->
        sessionLoaded && !operationInProgress
    }
}
