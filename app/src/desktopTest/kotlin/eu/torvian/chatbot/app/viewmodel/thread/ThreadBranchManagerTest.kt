package eu.torvian.chatbot.app.viewmodel.thread

import eu.torvian.chatbot.app.testutils.data.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ThreadBranchManagerTest {
    
    private lateinit var threadBranchManager: ThreadBranchManager
    
    @BeforeEach
    fun setUp() {
        threadBranchManager = ThreadBranchManager()
    }
    
    @Test
    fun buildThreadBranch_success_returnsCorrectBranch() {
        // Arrange
        val messages = allMockMessages
        val leafId = m3.id // Branch: m1 -> m2 -> m3
        
        // Act
        val result = threadBranchManager.buildThreadBranch(messages, leafId)
        
        // Assert
        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
        val branch = result.messages
        assertEquals(3, branch.size)
        assertEquals(m1.id, branch[0].id)
        assertEquals(m2.id, branch[1].id)
        assertEquals(m3.id, branch[2].id)
    }
    
    @Test
    fun buildThreadBranch_differentBranch_returnsCorrectBranch() {
        // Arrange
        val messages = allMockMessages
        val leafId = m5.id // Branch: m1 -> m4 -> m5
        
        // Act
        val result = threadBranchManager.buildThreadBranch(messages, leafId)
        
        // Assert
        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
        val branch = result.messages
        assertEquals(3, branch.size)
        assertEquals(m1.id, branch[0].id)
        assertEquals(m4.id, branch[1].id)
        assertEquals(m5.id, branch[2].id)
    }
    
    @Test
    fun buildThreadBranch_separateThread_returnsCorrectBranch() {
        // Arrange
        val messages = allMockMessages
        val leafId = m7.id // Branch: m6 -> m7
        
        // Act
        val result = threadBranchManager.buildThreadBranch(messages, leafId)
        
        // Assert
        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
        val branch = result.messages
        assertEquals(2, branch.size)
        assertEquals(m6.id, branch[0].id)
        assertEquals(m7.id, branch[1].id)
    }
    
    @Test
    fun buildThreadBranch_nullLeafId_returnsEmptySuccess() {
        // Arrange
        val messages = allMockMessages
        
        // Act
        val result = threadBranchManager.buildThreadBranch(messages, null)
        
        // Assert
        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Success)
        assertTrue(result.messages.isEmpty())
    }
    
    @Test
    fun buildThreadBranch_emptyMessages_returnsError() {
        // Arrange
        val messages = emptyList<eu.torvian.chatbot.common.models.ChatMessage>()
        val leafId = 1L
        
        // Act
        val result = threadBranchManager.buildThreadBranch(messages, leafId)
        
        // Assert
        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Error)
        assertEquals(ThreadBranchManager.ThreadBranchError.EMPTY_MESSAGE_LIST, result.cause)
    }
    
    @Test
    fun buildThreadBranch_missingMessage_returnsError() {
        // Arrange
        val messages = allMockMessages
        val leafId = 999L // Non-existent message
        
        // Act
        val result = threadBranchManager.buildThreadBranch(messages, leafId)
        
        // Assert
        assertTrue(result is ThreadBranchManager.ThreadBranchResult.Error)
        assertEquals(ThreadBranchManager.ThreadBranchError.BROKEN_LINK, result.cause)
    }
    
    @Test
    fun findLeafOfBranch_success_returnsLeafId() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val startMessageId = m1.id // Should find m3 as leaf (first branch)
        
        // Act
        val result = threadBranchManager.findLeafOfBranch(startMessageId, messageMap)
        
        // Assert
        assertEquals(m2.id, result) // m2 is the first child, and m3 is its child (leaf)
    }
    
    @Test
    fun findLeafOfBranch_alreadyLeaf_returnsSameId() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val startMessageId = m3.id // Already a leaf
        
        // Act
        val result = threadBranchManager.findLeafOfBranch(startMessageId, messageMap)
        
        // Assert
        assertEquals(m3.id, result)
    }
    
    @Test
    fun findLeafOfBranch_missingMessage_returnsNull() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val startMessageId = 999L // Non-existent message
        
        // Act
        val result = threadBranchManager.findLeafOfBranch(startMessageId, messageMap)
        
        // Assert
        assertNull(result)
    }
    
    @Test
    fun findAllLeafMessages_returnsCorrectLeaves() {
        // Arrange
        val messages = allMockMessages
        
        // Act
        val result = threadBranchManager.findAllLeafMessages(messages)
        
        // Assert
        assertEquals(3, result.size)
        assertTrue(result.contains(m3.id)) // Leaf of first branch
        assertTrue(result.contains(m5.id)) // Leaf of second branch
        assertTrue(result.contains(m7.id)) // Leaf of third branch
    }
    
    @Test
    fun findAllRootMessages_returnsCorrectRoots() {
        // Arrange
        val messages = allMockMessages
        
        // Act
        val result = threadBranchManager.findAllRootMessages(messages)
        
        // Assert
        assertEquals(2, result.size)
        assertTrue(result.contains(m1.id)) // Root of first thread
        assertTrue(result.contains(m6.id)) // Root of second thread
    }
    
    @Test
    fun validateMessageTree_validTree_returnsNoErrors() {
        // Arrange
        val messages = allMockMessages
        
        // Act
        val result = threadBranchManager.validateMessageTree(messages)
        
        // Assert
        assertTrue(result.isEmpty())
    }
    
    @Test
    fun validateMessageTree_missingParent_returnsError() {
        // Arrange
        val messages = listOf(
            userMessage(id = 1, sessionId = 100, content = "Child", parentMessageId = 999) // Parent doesn't exist
        )
        
        // Act
        val result = threadBranchManager.validateMessageTree(messages)
        
        // Assert
        assertEquals(1, result.size)
        assertTrue(result[0].contains("references non-existent parent 999"))
    }
    
    @Test
    fun validateMessageTree_missingChild_returnsError() {
        // Arrange
        val messages = listOf(
            userMessage(id = 1, sessionId = 100, content = "Parent", childrenMessageIds = listOf(999)) // Child doesn't exist
        )
        
        // Act
        val result = threadBranchManager.validateMessageTree(messages)
        
        // Assert
        assertEquals(1, result.size)
        assertTrue(result[0].contains("references non-existent child 999"))
    }
    
    @Test
    fun getPathToMessage_success_returnsCorrectPath() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val targetMessageId = m3.id
        
        // Act
        val result = threadBranchManager.getPathToMessage(targetMessageId, messageMap)
        
        // Assert
        assertEquals(3, result.size)
        assertEquals(m1.id, result[0])
        assertEquals(m2.id, result[1])
        assertEquals(m3.id, result[2])
    }
    
    @Test
    fun isAncestor_true_returnsTrue() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val ancestorId = m1.id
        val descendantId = m3.id
        
        // Act
        val result = threadBranchManager.isAncestor(ancestorId, descendantId, messageMap)
        
        // Assert
        assertTrue(result)
    }
    
    @Test
    fun isAncestor_false_returnsFalse() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val ancestorId = m3.id
        val descendantId = m1.id
        
        // Act
        val result = threadBranchManager.isAncestor(ancestorId, descendantId, messageMap)
        
        // Assert
        assertEquals(false, result)
    }
    
    @Test
    fun isAncestor_sameMessage_returnsFalse() {
        // Arrange
        val messageMap = allMockMessages.associateBy { it.id }
        val messageId = m1.id
        
        // Act
        val result = threadBranchManager.isAncestor(messageId, messageId, messageMap)
        
        // Assert
        assertEquals(false, result)
    }
}
