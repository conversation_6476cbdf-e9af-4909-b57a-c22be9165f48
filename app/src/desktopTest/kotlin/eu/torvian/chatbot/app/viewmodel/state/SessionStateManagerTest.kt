package eu.torvian.chatbot.app.viewmodel.state

import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.app.testutils.data.*
import eu.torvian.chatbot.app.testutils.misc.TestClock
import eu.torvian.chatbot.app.testutils.viewmodel.returnsDelayed
import eu.torvian.chatbot.app.testutils.viewmodel.startCollecting
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.UpdateSessionLeafMessageRequest
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

@ExperimentalCoroutinesApi
class SessionStateManagerTest {
    
    private val testDispatcher = StandardTestDispatcher()
    private lateinit var clock: TestClock
    private val sessionApi: SessionApi = mockk()
    
    private lateinit var sessionStateManager: SessionStateManager
    
    // Collected emissions for assertions
    private val collectedSessionStates = mutableListOf<UiState<ApiError, ChatSession>>()
    private val collectedLeafIds = mutableListOf<Long?>()
    
    @BeforeEach
    fun setUp() {
        val initialTime = LocalDateTime(2023, 1, 1, 10, 0, 0).toInstant(TimeZone.UTC)
        clock = TestClock(initialTime)
        
        sessionStateManager = SessionStateManager(
            sessionApi = sessionApi,
            scope = TestScope(testDispatcher),
            dispatcher = testDispatcher,
            clock = clock
        )
    }
    
    @AfterEach
    fun tearDown() {
        clearMocks(sessionApi)
    }
    
    private fun TestScope.startCollecting() {
        startCollecting(sessionStateManager.sessionState, collectedSessionStates)
        startCollecting(sessionStateManager.currentBranchLeafId, collectedLeafIds)
        advanceUntilIdle()
        runCurrent()
    }
    
    private fun clearCollected() {
        collectedSessionStates.clear()
        collectedLeafIds.clear()
    }
    
    @Test
    fun loadSession_success_updatesState() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
        
        startCollecting()
        clearCollected()
        
        // Act
        sessionStateManager.loadSession(session.id)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(2, collectedSessionStates.size)
        assertEquals(UiState.Loading, collectedSessionStates[0])
        assertEquals(UiState.Success(session), collectedSessionStates[1])
        
        assertEquals(1, collectedLeafIds.size)
        assertEquals(session.currentLeafMessageId, collectedLeafIds[0])
        
        coVerify(exactly = 1) { sessionApi.getSessionDetails(session.id) }
    }
    
    @Test
    fun loadSession_error_updatesState() = runTest(testDispatcher) {
        // Arrange
        val sessionId = 123L
        val error = genericApiError(statusCode = 404, code = "not-found", message = "Session not found")
        coEvery { sessionApi.getSessionDetails(sessionId) }.returnsDelayed(left(error))
        
        var errorCallbackCalled = false
        var capturedError: ApiError? = null
        
        startCollecting()
        clearCollected()
        
        // Act
        sessionStateManager.loadSession(sessionId) { apiError ->
            errorCallbackCalled = true
            capturedError = apiError
        }
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(2, collectedSessionStates.size)
        assertEquals(UiState.Loading, collectedSessionStates[0])
        assertEquals(UiState.Error(error), collectedSessionStates[1])
        
        assertEquals(true, errorCallbackCalled)
        assertEquals(error, capturedError)
        
        coVerify(exactly = 1) { sessionApi.getSessionDetails(sessionId) }
    }
    
    @Test
    fun updateSession_updatesState() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        startCollecting()
        clearCollected()
        
        // Act
        sessionStateManager.updateSession(session)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(1, collectedSessionStates.size)
        assertEquals(UiState.Success(session), collectedSessionStates[0])
    }
    
    @Test
    fun updateCurrentBranchLeafId_updatesState() = runTest(testDispatcher) {
        // Arrange
        val leafId = 42L
        startCollecting()
        clearCollected()
        
        // Act
        sessionStateManager.updateCurrentBranchLeafId(leafId)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(1, collectedLeafIds.size)
        assertEquals(leafId, collectedLeafIds[0])
    }
    
    @Test
    fun updateSessionLeafMessage_success_updatesStateAndCallsApi() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        val newLeafId = 99L
        
        sessionStateManager.updateSession(session)
        coEvery { 
            sessionApi.updateSessionLeafMessage(session.id, UpdateSessionLeafMessageRequest(newLeafId)) 
        }.returnsDelayed(right(Unit))
        
        startCollecting()
        clearCollected()
        
        var successCallbackCalled = false
        
        // Act
        sessionStateManager.updateSessionLeafMessage(
            leafId = newLeafId,
            onSuccess = { successCallbackCalled = true }
        )
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(1, collectedLeafIds.size)
        assertEquals(newLeafId, collectedLeafIds[0])
        
        assertEquals(1, collectedSessionStates.size)
        val updatedSession = collectedSessionStates[0].dataOrNull
        assertEquals(newLeafId, updatedSession?.currentLeafMessageId)
        
        assertEquals(true, successCallbackCalled)
        
        coVerify(exactly = 1) { 
            sessionApi.updateSessionLeafMessage(session.id, UpdateSessionLeafMessageRequest(newLeafId)) 
        }
    }
    
    @Test
    fun addMessagesToSession_updatesSessionWithNewMessages() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        val newMessages = listOf(
            userMessage(id = 100, sessionId = session.id, content = "New user message"),
            assistantMessage(id = 101, sessionId = session.id, content = "New assistant message")
        )
        val newLeafId = 101L
        
        sessionStateManager.updateSession(session)
        startCollecting()
        clearCollected()
        
        // Act
        sessionStateManager.addMessagesToSession(newMessages, newLeafId)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(1, collectedSessionStates.size)
        val updatedSession = collectedSessionStates[0].dataOrNull
        assertEquals(session.messages.size + 2, updatedSession?.messages?.size)
        assertEquals(newLeafId, updatedSession?.currentLeafMessageId)
        
        assertEquals(1, collectedLeafIds.size)
        assertEquals(newLeafId, collectedLeafIds[0])
    }
    
    @Test
    fun currentSession_returnsCorrectValue() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        
        // Act & Assert - Initially null
        assertNull(sessionStateManager.currentSession)
        
        // Update session
        sessionStateManager.updateSession(session)
        assertEquals(session, sessionStateManager.currentSession)
        
        // Set to error state
        sessionStateManager.loadSession(999L) // Non-existent session
        coEvery { sessionApi.getSessionDetails(999L) }.returnsDelayed(left(notFoundError()))
        advanceUntilIdle()
        runCurrent()
        
        assertNull(sessionStateManager.currentSession)
    }
}
