package eu.torvian.chatbot.app.viewmodel

import eu.torvian.chatbot.app.domain.contracts.UiState
import eu.torvian.chatbot.app.service.api.ChatApi
import eu.torvian.chatbot.app.service.api.SessionApi
import eu.torvian.chatbot.app.service.api.SettingsApi
import eu.torvian.chatbot.app.service.misc.EventBus
import eu.torvian.chatbot.app.testutils.data.*
import eu.torvian.chatbot.app.testutils.misc.TestClock
import eu.torvian.chatbot.app.testutils.viewmodel.returnsDelayed
import eu.torvian.chatbot.app.testutils.viewmodel.startCollecting
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.models.ChatMessage
import eu.torvian.chatbot.common.models.ChatSession
import eu.torvian.chatbot.common.models.ProcessNewMessageRequest
import eu.torvian.chatbot.common.models.UpdateMessageRequest
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.test.*
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
class RefactoredChatViewModelTest {
    
    private val testDispatcher = StandardTestDispatcher()
    private lateinit var clock: TestClock
    
    // Mock dependencies
    private val sessionApi: SessionApi = mockk()
    private val chatApi: ChatApi = mockk()
    private val settingsApi: SettingsApi = mockk()
    private val eventBus: EventBus = mockk(relaxed = true)
    
    // Mock SharedFlow for EventBus events
    private val mockEventsFlow = MutableSharedFlow<eu.torvian.chatbot.app.domain.events.AppEvent>()
    
    // The ViewModel instance to test
    private lateinit var viewModel: ChatViewModel
    
    // Collected emissions for assertions
    private val collectedSessionStates = mutableListOf<UiState<ApiError, ChatSession>>()
    private val collectedDisplayedMessages = mutableListOf<List<ChatMessage>>()
    private val collectedInputContent = mutableListOf<String>()
    
    @BeforeEach
    fun setUp() {
        val initialTime = LocalDateTime(2023, 1, 1, 10, 0, 0).toInstant(TimeZone.UTC)
        clock = TestClock(initialTime)
        
        // Set up EventBus mock
        every { eventBus.events } returns mockEventsFlow
    }
    
    @AfterEach
    fun tearDown() {
        clearMocks(sessionApi, chatApi, settingsApi, eventBus)
    }
    
    private fun TestScope.initViewModelAndCollect() {
        viewModel = ChatViewModel(sessionApi, chatApi, settingsApi, eventBus, testDispatcher, clock)
        
        startCollecting(viewModel.sessionState, collectedSessionStates)
        startCollecting(viewModel.displayedMessages, collectedDisplayedMessages)
        startCollecting(viewModel.inputContent, collectedInputContent)
        
        advanceUntilIdle()
        runCurrent()
    }
    
    private fun clearCollected() {
        collectedSessionStates.clear()
        collectedDisplayedMessages.clear()
        collectedInputContent.clear()
    }
    
    @Test
    fun loadSession_success_updatesStateCorrectly() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
        
        initViewModelAndCollect()
        clearCollected()
        
        // Act
        viewModel.loadSession(session.id)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(2, collectedSessionStates.size)
        assertEquals(UiState.Loading, collectedSessionStates[0])
        assertEquals(UiState.Success(session), collectedSessionStates[1])
        
        // Should display the correct branch
        assertTrue(collectedDisplayedMessages.isNotEmpty())
        val displayedMessages = collectedDisplayedMessages.last()
        assertEquals(3, displayedMessages.size) // m1 -> m2 -> m3
        assertEquals(m1.id, displayedMessages[0].id)
        assertEquals(m2.id, displayedMessages[1].id)
        assertEquals(m3.id, displayedMessages[2].id)
        
        coVerify(exactly = 1) { sessionApi.getSessionDetails(session.id) }
    }
    
    @Test
    fun updateInput_updatesInputContent() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        clearCollected()
        
        // Act
        viewModel.updateInput("Hello, world!")
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(1, collectedInputContent.size)
        assertEquals("Hello, world!", collectedInputContent[0])
        assertEquals("Hello, world!", viewModel.inputContent.value)
    }
    
    @Test
    fun startReplyTo_setsReplyTarget() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        
        // Act
        viewModel.startReplyTo(m2)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(m2, viewModel.replyTargetMessage.value)
    }
    
    @Test
    fun cancelReply_clearsReplyTarget() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        viewModel.startReplyTo(m2)
        
        // Act
        viewModel.cancelReply()
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(null, viewModel.replyTargetMessage.value)
    }
    
    @Test
    fun startEditing_setsEditingState() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        
        // Act
        viewModel.startEditing(m2)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(m2, viewModel.editingMessage.value)
        assertEquals(m2.content, viewModel.editingContent.value)
    }
    
    @Test
    fun updateEditingContent_updatesEditingContent() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        viewModel.startEditing(m2)
        
        // Act
        viewModel.updateEditingContent("Updated content")
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals("Updated content", viewModel.editingContent.value)
    }
    
    @Test
    fun cancelEditing_clearsEditingState() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        viewModel.startEditing(m2)
        
        // Act
        viewModel.cancelEditing()
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(null, viewModel.editingMessage.value)
        assertEquals("", viewModel.editingContent.value)
    }
    
    @Test
    fun saveEditing_success_updatesMessage() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        val updatedMessage = m2.copy(content = "Updated content")
        
        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
        coEvery { 
            chatApi.updateMessageContent(m2.id, UpdateMessageRequest("Updated content")) 
        }.returnsDelayed(right(updatedMessage))
        
        initViewModelAndCollect()
        viewModel.loadSession(session.id)
        advanceUntilIdle()
        runCurrent()
        
        viewModel.startEditing(m2)
        viewModel.updateEditingContent("Updated content")
        clearCollected()
        
        // Act
        viewModel.saveEditing()
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals(null, viewModel.editingMessage.value)
        assertEquals("", viewModel.editingContent.value)
        
        coVerify(exactly = 1) { 
            chatApi.updateMessageContent(m2.id, UpdateMessageRequest("Updated content")) 
        }
    }
    
    @Test
    fun deleteMessage_success_reloadsSession() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        
        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
        coEvery { chatApi.deleteMessage(m2.id) }.returnsDelayed(right(Unit))
        
        initViewModelAndCollect()
        viewModel.loadSession(session.id)
        advanceUntilIdle()
        runCurrent()
        clearCollected()
        
        // Act
        viewModel.deleteMessage(m2.id)
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        coVerify(exactly = 1) { chatApi.deleteMessage(m2.id) }
        coVerify(exactly = 2) { sessionApi.getSessionDetails(session.id) } // Initial load + reload after delete
    }
    
    @Test
    fun sendMessage_emptyContent_doesNothing() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
        
        initViewModelAndCollect()
        viewModel.loadSession(session.id)
        advanceUntilIdle()
        runCurrent()
        
        viewModel.updateInput("   ") // Whitespace only
        clearCollected()
        
        // Act
        viewModel.sendMessage()
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals("   ", viewModel.inputContent.value) // Should not be cleared
        assertFalse(viewModel.isSendingMessage.value)
        
        // Verify no API calls were made
        coVerify(exactly = 0) { chatApi.processNewMessage(any(), any()) }
        coVerify(exactly = 0) { chatApi.processNewMessageStreaming(any(), any()) }
    }
    
    @Test
    fun sendMessage_noSessionLoaded_doesNothing() = runTest(testDispatcher) {
        // Arrange
        initViewModelAndCollect()
        viewModel.updateInput("Test message")
        clearCollected()
        
        // Act
        viewModel.sendMessage()
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals("Test message", viewModel.inputContent.value) // Should not be cleared
        assertFalse(viewModel.isSendingMessage.value)
        
        // Verify no API calls were made
        coVerify(exactly = 0) { chatApi.processNewMessage(any(), any()) }
        coVerify(exactly = 0) { chatApi.processNewMessageStreaming(any(), any()) }
    }
    
    @Test
    fun clearSession_clearsAllState() = runTest(testDispatcher) {
        // Arrange
        val session = mockSession2_threaded
        coEvery { sessionApi.getSessionDetails(session.id) }.returnsDelayed(right(session))
        
        initViewModelAndCollect()
        viewModel.loadSession(session.id)
        viewModel.updateInput("Some input")
        viewModel.startReplyTo(m2)
        viewModel.startEditing(m3)
        advanceUntilIdle()
        runCurrent()
        
        // Act
        viewModel.clearSession()
        advanceUntilIdle()
        runCurrent()
        
        // Assert
        assertEquals("", viewModel.inputContent.value)
        assertEquals(null, viewModel.replyTargetMessage.value)
        assertEquals(null, viewModel.editingMessage.value)
        assertEquals("", viewModel.editingContent.value)
        assertFalse(viewModel.isSendingMessage.value)
    }
}
